# AI-Powered Personal Finance Advisor Backend Dependencies
# From <PERSON><PERSON>'s Workspace

# FastAPI and server
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# Data processing and analysis
pandas==2.1.3
numpy==1.25.2
scikit-learn==1.3.2
joblib==1.3.2

# Time series forecasting
statsmodels==0.14.0

# Natural language processing
nltk==3.8.1

# Synthetic data generation
Faker==20.1.0

# HTTP requests
requests==2.31.0

# Environment and configuration
python-dotenv==1.0.0

# Logging and monitoring
structlog==23.2.0

# Data validation
pydantic==2.5.0

# CORS middleware
python-jose[cryptography]==3.3.0

# Optional: Advanced ML capabilities
# tensorflow==2.15.0  # Uncomment if needed
# torch==2.1.0  # Uncomment if needed
