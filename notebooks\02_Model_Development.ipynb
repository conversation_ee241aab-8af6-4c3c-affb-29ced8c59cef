{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Financial Analysis Model Development\n", "**From <PERSON><PERSON>'s Workspace**\n", "\n", "This notebook develops and evaluates machine learning models for financial analysis including spending segmentation, anomaly detection, and forecasting."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.cluster import KMeans\n", "from sklearn.ensemble import IsolationForest\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.metrics import silhouette_score\n", "import joblib\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set style\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"Libraries imported successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Data Loading and Preparation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load processed data\n", "try:\n", "    df = pd.read_csv('../data/processed_transactions.csv')\n", "    df['date'] = pd.to_datetime(df['date'])\n", "    print(f\"Loaded {len(df)} transactions\")\nexcept FileNotFoundError:\n", "    print(\"Processed data not found. Generating synthetic data...\")\n", "    import sys\n", "    sys.path.append('../scripts')\n", "    from generate_synthetic_data import generate_transactions\n", "    df = generate_transactions(1000)\n", "    df['date'] = pd.to_datetime(df['date'])\n", "\n", "df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Spending Behavior Segmentation with K-Means"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Prepare features for clustering\n", "def prepare_clustering_features(df):\n", "    features = ['amount']\n", "    df_features = df.copy()\n", "    \n", "    # One-hot encode categorical features\n", "    if 'category' in df_features.columns:\n", "        category_dummies = pd.get_dummies(df_features['category'], prefix='cat')\n", "        df_features = pd.concat([df_features, category_dummies], axis=1)\n", "        features.extend(category_dummies.columns.tolist())\n", "    \n", "    if 'day_of_week' in df_features.columns:\n", "        dow_dummies = pd.get_dummies(df_features['day_of_week'], prefix='day')\n", "        df_features = pd.concat([df_features, dow_dummies], axis=1)\n", "        features.extend(dow_dummies.columns.tolist())\n", "    \n", "    return df_features[features].fillna(0)\n", "\n", "# Prepare features\n", "X_clustering = prepare_clustering_features(df)\n", "print(f\"Clustering features shape: {X_clustering.shape}\")\n", "print(f\"Features: {X_clustering.columns.tolist()[:10]}...\")  # Show first 10 features"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Determine optimal number of clusters using elbow method\n", "scaler = StandardScaler()\n", "X_scaled = scaler.fit_transform(X_clustering)\n", "\n", "# Calculate WCSS for different cluster numbers\n", "wcss = []\n", "silhouette_scores = []\n", "k_range = range(2, 11)\n", "\n", "for k in k_range:\n", "    kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)\n", "    kmeans.fit(X_scaled)\n", "    wcss.append(kmeans.inertia_)\n", "    \n", "    if len(X_scaled) > k:\n", "        sil_score = silhouette_score(X_scaled, kmeans.labels_)\n", "        silhouette_scores.append(sil_score)\n", "    else:\n", "        silhouette_scores.append(0)\n", "\n", "# Plot elbow curve and silhouette scores\n", "plt.figure(figsize=(12, 5))\n", "\n", "plt.subplot(1, 2, 1)\n", "plt.plot(k_range, wcss, 'bo-')\n", "plt.title('Elbow Method for Optimal k')\n", "plt.xlabel('Number of Clusters (k)')\n", "plt.y<PERSON><PERSON>('WCSS')\n", "plt.grid(True)\n", "\n", "plt.subplot(1, 2, 2)\n", "plt.plot(k_range, silhouette_scores, 'ro-')\n", "plt.title('Si<PERSON><PERSON>ette Score vs Number of Clusters')\n", "plt.xlabel('Number of Clusters (k)')\n", "plt.ylabel('Silhouette Score')\n", "plt.grid(True)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Find optimal k\n", "optimal_k = k_range[np.argmax(silhouette_scores)]\n", "print(f\"Optimal number of clusters: {optimal_k}\")\n", "print(f\"Best silhouette score: {max(silhouette_scores):.3f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Train final K-means model\n", "final_kmeans = KMeans(n_clusters=optimal_k, random_state=42, n_init=10)\n", "cluster_labels = final_kmeans.fit_predict(X_scaled)\n", "\n", "# Add cluster labels to dataframe\n", "df_clustered = df.copy()\n", "df_clustered['cluster'] = cluster_labels\n", "\n", "# Analyze clusters\n", "cluster_analysis = df_clustered.groupby('cluster').agg({\n", "    'amount': ['count', 'mean', 'sum', 'std'],\n", "    'category': lambda x: x.value_counts().index[0]  # Most common category\n", "}).round(2)\n", "\n", "print(\"Cluster Analysis:\")\n", "print(cluster_analysis)\n", "\n", "# Save the model\n", "joblib.dump(final_kmeans, '../models/kmeans_model.joblib')\n", "joblib.dump(scaler, '../models/scaler_clustering.joblib')\n", "print(\"\\nK-means model and scaler saved!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize clusters\n", "plt.figure(figsize=(15, 5))\n", "\n", "# Cluster distribution\n", "plt.subplot(1, 3, 1)\n", "cluster_counts = df_clustered['cluster'].value_counts().sort_index()\n", "plt.bar(cluster_counts.index, cluster_counts.values)\n", "plt.title('Cluster Distribution')\n", "plt.xlabel('Cluster')\n", "plt.ylabel('Number of Transactions')\n", "\n", "# Amount by cluster\n", "plt.subplot(1, 3, 2)\n", "df_clustered.boxplot(column='amount', by='cluster', ax=plt.gca())\n", "plt.title('Transaction Amount by Cluster')\n", "plt.suptitle('')  # Remove default title\n", "\n", "# Category distribution by cluster\n", "plt.subplot(1, 3, 3)\n", "cluster_category = pd.crosstab(df_clustered['cluster'], df_clustered['category'])\n", "cluster_category_pct = cluster_category.div(cluster_category.sum(axis=1), axis=0)\n", "sns.heatmap(cluster_category_pct, annot=True, fmt='.2f', cmap='Blues')\n", "plt.title('Category Distribution by Cluster')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Anomaly Detection with Isolation Forest"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Prepare features for anomaly detection\n", "def prepare_anomaly_features(df):\n", "    features = ['amount']\n", "    df_features = df.copy()\n", "    \n", "    # Add day of week as numerical\n", "    if 'day_of_week' in df_features.columns:\n", "        day_mapping = {\n", "            'Monday': 0, 'Tuesday': 1, 'Wednesday': 2, 'Thursday': 3,\n", "            'Friday': 4, 'Saturday': 5, 'Sunday': 6\n", "        }\n", "        df_features['day_of_week_num'] = df_features['day_of_week'].map(day_mapping)\n", "        features.append('day_of_week_num')\n", "    \n", "    return df_features[features].dropna()\n", "\n", "# Prepare features\n", "X_anomaly = prepare_anomaly_features(df)\n", "print(f\"Anomaly detection features shape: {X_anomaly.shape}\")\n", "print(f\"Features: {X_anomaly.columns.tolist()}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Train Isolation Forest model\n", "scaler_anomaly = StandardScaler()\n", "X_anomaly_scaled = scaler_anomaly.fit_transform(X_anomaly)\n", "\n", "# Try different contamination values\n", "contamination_values = [0.05, 0.1, 0.15, 0.2]\n", "results = []\n", "\n", "for contamination in contamination_values:\n", "    iso_forest = IsolationForest(\n", "        n_estimators=100,\n", "        contamination=contamination,\n", "        random_state=42\n", "    )\n", "    \n", "    anomaly_labels = iso_forest.fit_predict(X_anomaly_scaled)\n", "    anomaly_count = np.sum(anomaly_labels == -1)\n", "    anomaly_percentage = (anomaly_count / len(X_anomaly)) * 100\n", "    \n", "    results.append({\n", "        'contamination': contamination,\n", "        'anomaly_count': anomaly_count,\n", "        'anomaly_percentage': anomaly_percentage\n", "    })\n", "\n", "results_df = pd.DataFrame(results)\n", "print(\"Contamination Analysis:\")\n", "print(results_df)\n", "\n", "# Choose optimal contamination (e.g., 0.1 for 10%)\n", "optimal_contamination = 0.1\n", "print(f\"\\nUsing contamination: {optimal_contamination}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Train final Isolation Forest model\n", "final_iso_forest = IsolationForest(\n", "    n_estimators=100,\n", "    contamination=optimal_contamination,\n", "    random_state=42\n", ")\n", "\n", "anomaly_labels = final_iso_forest.fit_predict(X_anomaly_scaled)\n", "anomaly_scores = final_iso_forest.decision_function(X_anomaly_scaled)\n", "\n", "# Add results to dataframe\n", "df_anomaly = df.iloc[:len(X_anomaly)].copy()\n", "df_anomaly['anomaly_score'] = anomaly_scores\n", "df_anomaly['is_anomaly'] = (anomaly_labels == -1)\n", "\n", "# Analyze anomalies\n", "anomalies = df_anomaly[df_anomaly['is_anomaly']]\n", "print(f\"Detected {len(anomalies)} anomalies ({len(anomalies)/len(df_anomaly)*100:.2f}%)\")\n", "\n", "if len(anomalies) > 0:\n", "    print(\"\\nTop 10 anomalous transactions:\")\n", "    top_anomalies = anomalies.nsmallest(10, 'anomaly_score')\n", "    print(top_anomalies[['date', 'description', 'amount', 'category', 'anomaly_score']])\n", "\n", "# Save the model\n", "joblib.dump(final_iso_forest, '../models/isolation_forest_model.joblib')\n", "joblib.dump(scaler_anomaly, '../models/scaler_anomaly.joblib')\n", "print(\"\\nIsolation Forest model and scaler saved!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize anomalies\n", "plt.figure(figsize=(15, 5))\n", "\n", "# Anomaly score distribution\n", "plt.subplot(1, 3, 1)\n", "plt.hist(df_anomaly['anomaly_score'], bins=50, alpha=0.7)\n", "plt.axvline(x=final_iso_forest.offset_, color='red', linestyle='--', label='Threshold')\n", "plt.title('Anomaly Score Distribution')\n", "plt.xlabel('Anomaly Score')\n", "plt.ylabel('Frequency')\n", "plt.legend()\n", "\n", "# Amount vs anomaly score\n", "plt.subplot(1, 3, 2)\n", "colors = ['red' if x else 'blue' for x in df_anomaly['is_anomaly']]\n", "plt.scatter(df_anomaly['amount'], df_anomaly['anomaly_score'], c=colors, alpha=0.6)\n", "plt.title('Amount vs Anomaly Score')\n", "plt.xlabel('Transaction Amount')\n", "plt.ylabel('Anomaly Score')\n", "\n", "# Anomalies over time\n", "plt.subplot(1, 3, 3)\n", "anomaly_dates = anomalies['date']\n", "anomaly_amounts = anomalies['amount']\n", "plt.scatter(anomaly_dates, anomaly_amounts, color='red', alpha=0.7)\n", "plt.title('Anomalous Transactions Over Time')\n", "plt.xlabel('Date')\n", "plt.ylabel('Amount')\n", "plt.xticks(rotation=45)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Time Series Analysis for Forecasting"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Prepare time series data\n", "df_ts = df.copy()\n", "df_ts['date'] = pd.to_datetime(df_ts['date'])\n", "df_ts = df_ts.sort_values('date')\n", "\n", "# Create daily spending series\n", "daily_spending = df_ts[df_ts['transaction_type'] == 'Debit'].groupby('date')['amount'].sum()\n", "daily_spending = daily_spending.asfreq('D', fill_value=0)\n", "\n", "print(f\"Time series length: {len(daily_spending)} days\")\n", "print(f\"Date range: {daily_spending.index.min()} to {daily_spending.index.max()}\")\n", "print(f\"Average daily spending: ${daily_spending.mean():.2f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Time series visualization and basic analysis\n", "plt.figure(figsize=(15, 10))\n", "\n", "# Original time series\n", "plt.subplot(2, 2, 1)\n", "plt.plot(daily_spending.index, daily_spending.values)\n", "plt.title('Daily Spending Time Series')\n", "plt.xlabel('Date')\n", "plt.ylabel('Amount ($)')\n", "\n", "# Rolling statistics\n", "plt.subplot(2, 2, 2)\n", "rolling_mean = daily_spending.rolling(window=7).mean()\n", "rolling_std = daily_spending.rolling(window=7).std()\n", "plt.plot(daily_spending.index, daily_spending.values, alpha=0.3, label='Original')\n", "plt.plot(rolling_mean.index, rolling_mean.values, label='7-day MA')\n", "plt.fill_between(rolling_mean.index, \n", "                 rolling_mean - rolling_std, \n", "                 rolling_mean + rolling_std, \n", "                 alpha=0.2, label='±1 Std')\n", "plt.title('Rolling Statistics')\n", "plt.xlabel('Date')\n", "plt.ylabel('Amount ($)')\n", "plt.legend()\n", "\n", "# Distribution\n", "plt.subplot(2, 2, 3)\n", "plt.hist(daily_spending.values, bins=30, alpha=0.7)\n", "plt.title('Daily Spending Distribution')\n", "plt.xlabel('Amount ($)')\n", "plt.ylabel('Frequency')\n", "\n", "# Autocorrelation (simple lag plot)\n", "plt.subplot(2, 2, 4)\n", "lag_1 = daily_spending.shift(1)\n", "plt.scatter(lag_1, daily_spending, alpha=0.6)\n", "plt.title('Lag-1 Autocorrelation')\n", "plt.xlabel('Previous Day Spending')\n", "plt.ylabel('Current Day Spending')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Model Evaluation and Summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Model summary\n", "print(\"=\" * 50)\n", "print(\"MODEL DEVELOPMENT SUMMARY\")\n", "print(\"=\" * 50)\n", "\n", "print(f\"\\n1. SPENDING SEGMENTATION (K-Means)\")\n", "print(f\"   - Optimal clusters: {optimal_k}\")\n", "print(f\"   - Silhouette score: {max(silhouette_scores):.3f}\")\n", "print(f\"   - Features used: {len(X_clustering.columns)}\")\n", "\n", "print(f\"\\n2. ANOMALY DETECTION (Isolation Forest)\")\n", "print(f\"   - Contamination rate: {optimal_contamination}\")\n", "print(f\"   - Anomalies detected: {len(anomalies)} ({len(anomalies)/len(df_anomaly)*100:.2f}%)\")\n", "print(f\"   - Features used: {len(X_anomaly.columns)}\")\n", "\n", "print(f\"\\n3. TIME SERIES ANALYSIS\")\n", "print(f\"   - Time series length: {len(daily_spending)} days\")\n", "print(f\"   - Average daily spending: ${daily_spending.mean():.2f}\")\n", "print(f\"   - Spending volatility (std): ${daily_spending.std():.2f}\")\n", "\n", "print(f\"\\n4. SAVED MODELS\")\n", "print(f\"   - K-means model: ../models/kmeans_model.joblib\")\n", "print(f\"   - Clustering scaler: ../models/scaler_clustering.joblib\")\n", "print(f\"   - Isolation Forest: ../models/isolation_forest_model.joblib\")\n", "print(f\"   - Anomaly scaler: ../models/scaler_anomaly.joblib\")\n", "\n", "print(\"\\n\" + \"=\" * 50)\n", "print(\"Models ready for deployment!\")\n", "print(\"=\" * 50)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 4}